# 车身域控制器系统 (Body Domain Controller System)

## 项目概述

本项目是一个硕士研究生项目，与汽车公司合作开发demo级别的车身域控制器系统。采用分布式架构，基于SOME/IP协议实现电脑端与STM32H7之间的通信。

## 系统架构

```
┌─────────────────┐    SOME/IP over TCP/IP    ┌─────────────────┐
│   电脑端(PC)     │ ◄─────────────────────► │  STM32H7节点    │
│                 │      以太网(Ethernet)      │                 │
│ - 应用层服务     │                          │ - 底层控制逻辑   │
│ - SOME/IP客户端  │                          │ - SOME/IP服务端  │
│ - vsomeip库     │                          │ - vsomeip移植版  │
└─────────────────┘                          └─────────────────┘
```

## 技术栈

- **通信协议**: SOME/IP (基于vsomeip开源库)
- **服务架构**: SOA (面向服务的架构)
- **网络协议**: TCP/IP协议栈
- **物理层**: 以太网(Ethernet)
- **嵌入式协议栈**: LwIP

## 团队分工

- **我**: 电脑端应用层开发和通信节点实现
- **同学A**: SOME/IP协议栈移植到STM32和嵌入式通信节点开发
- **同学B**: 以太网通信和LwIP协议栈集成
- **同学C**: 硬件电路原理图设计

## 项目结构

```
car/
├── docs/                    # 项目文档
│   ├── architecture.md     # 架构设计文档
│   ├── protocol.md         # 协议定义文档
│   └── api.md              # API接口文档
├── pc-side/                # 电脑端代码
│   ├── src/                # 源代码
│   ├── include/            # 头文件
│   ├── config/             # 配置文件
│   └── tests/              # 测试代码
├── stm32-side/             # STM32端代码
│   ├── Core/               # STM32 HAL核心代码
│   ├── Middlewares/        # 中间件(vsomeip移植版)
│   ├── LWIP/               # LwIP协议栈
│   └── Application/        # 应用层代码
├── common/                 # 公共代码和定义
│   ├── protocol/           # 协议定义
│   ├── interfaces/         # 接口定义
│   └── utils/              # 工具函数
├── tools/                  # 开发工具
│   ├── code-generator/     # 代码生成工具
│   └── test-tools/         # 测试工具
└── examples/               # 示例代码
    ├── basic-communication/
    └── service-discovery/
```

## 开发阶段

### 阶段1: 基础架构搭建
- [x] 项目结构设计
- [ ] 通信协议定义
- [ ] 接口规范制定

### 阶段2: 电脑端开发
- [ ] vsomeip环境搭建
- [ ] 服务提供者实现
- [ ] 服务消费者实现

### 阶段3: STM32端开发
- [ ] vsomeip移植
- [ ] LwIP集成
- [ ] 底层服务实现

### 阶段4: 集成测试
- [ ] 端到端通信测试
- [ ] 性能测试
- [ ] 可靠性测试

## 快速开始

### 环境要求

**电脑端:**
- Ubuntu 20.04+ 或 Windows 10+
- GCC 9.0+ 或 MSVC 2019+
- CMake 3.16+
- vsomeip 3.1.20+

**STM32端:**
- STM32CubeIDE
- STM32H7系列开发板
- LwIP 2.1.2+

### 编译运行

详细的编译和运行说明请参考各子目录下的README文件。

## 贡献指南

1. Fork 本仓库
2. 创建特性分支 (`git checkout -b feature/AmazingFeature`)
3. 提交更改 (`git commit -m 'Add some AmazingFeature'`)
4. 推送到分支 (`git push origin feature/AmazingFeature`)
5. 开启 Pull Request

## 许可证

本项目仅用于学术研究目的。

## 联系方式

如有问题，请联系项目团队成员。
